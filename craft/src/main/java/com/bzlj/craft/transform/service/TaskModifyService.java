package com.bzlj.craft.transform.service;


import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.ProductionTaskExtendRepository;
import com.bzlj.craft.repository.ProductionTaskRepository;
import com.bzlj.craft.service.ICraftService;
import com.bzlj.craft.util.DateTimeConverter;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 任务属性修改处理器
 * <p>
 * 负责处理任务属性修改消息的转换和处理，主要功能包括：
 * 1. 接收任务属性修改消息
 * 2. 动态更新任务的各种属性
 * 3. 处理任务扩展属性的更新
 * 4. 支持批量任务属性修改
 * 5. 支持多种数据类型的属性转换
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class TaskModifyService {

    /**
     * 生产任务仓储，用于管理生产任务数据
     */
    @Autowired
    private ProductionTaskRepository productionTaskRepository;

    /**
     * 生产任务扩展仓储，用于管理任务扩展信息
     */
    @Autowired
    private ProductionTaskExtendRepository productionTaskExtendRepository;

    @Autowired
    private ICraftService craftService;

    /**
     * 转换处理任务属性修改消息
     * <p>
     * 处理任务属性修改JSON数据的核心方法，包括以下步骤：
     * 1. 支持数组格式的批量处理
     * 2. 解析任务编码并查询对应任务
     * 3. 动态更新任务属性
     * 4. 保存更新后的任务数据
     * </p>
     *
     * @param json 任务属性修改的JSON字符串
     * @throws RuntimeException 当任务编码为空或任务不存在时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void taskModifyTransform(String json, Map<String, Object> handleContext){
        JsonNode jsonNode = JsonUtils.toJsonNode(json);

        // 支持数组格式的批量处理
        if(jsonNode instanceof ArrayNode){
            jsonNode.forEach(node -> {
                taskModifyTransform(node.toString(), handleContext);
            });
            return;
        }

        // 1. 解析 taskCode
        JsonNode taskCodeNode = jsonNode.get("taskCode");
        if (taskCodeNode == null || taskCodeNode.isNull()) {
            throw new RuntimeException("taskCode 不能为空");
        }
        String taskCode = taskCodeNode.asText();

        // 2. 查询 ProductionTask
        ProductionTask task = productionTaskRepository.findFirstByTaskCode(taskCode);
        if (task == null) {
            throw new RuntimeException(String.format("任务不存在，任务号：%s", taskCode));
        }

        log.info("开始更新任务属性，任务号：{}", taskCode);

        // 3. 动态更新任务属性
        updateTaskProperties(task, jsonNode);

        // 4. 保存更新后的任务
        productionTaskRepository.save(task);
        craftService.updateStepExecTime(task.getTaskId());
        log.info("任务属性更新完成，任务号：{}", taskCode);
    }

    private void updateEquipment(ProductionTask task, JsonNode jsonNode) {
        if(Objects.isNull(jsonNode.get("equipmentCode"))){
            return;
        }
        Equipment equipment = transformEquipment(jsonNode.get("equipmentCode").asText());
        TaskEquipment taskEquipment = new TaskEquipment();
        taskEquipment.setId(new TaskEquipmentId(productionTask.getTaskId(), equipment.getId()));
        taskEquipment.setTask(productionTask);
        taskEquipment.setEquip(equipment);
        taskEquipmentRepository.save(taskEquipment);
    }

    /**
     * 动态更新任务属性
     * @param task 要更新的任务对象
     * @param jsonNode JSON节点包含要更新的属性
     */
    private void updateTaskProperties(ProductionTask task, JsonNode jsonNode) {
        // 获取 ProductionTask 的所有字段
        Field[] fields = ProductionTask.class.getDeclaredFields();
        Map<String, Field> fieldMap = new HashMap<>();
        for (Field field : fields) {
            fieldMap.put(field.getName(), field);
        }

        // 遍历 JSON 中的所有字段
        Iterator<Map.Entry<String, JsonNode>> fieldsIterator = jsonNode.fields();
        while (fieldsIterator.hasNext()) {
            Map.Entry<String, JsonNode> entry = fieldsIterator.next();
            String fieldName = entry.getKey();
            JsonNode fieldValue = entry.getValue();

            // 跳过 taskCode 字段，因为它用于查询，不需要更新
            if ("taskCode".equals(fieldName)) {
                continue;
            }

            // 处理扩展属性
            if ("extendAttr".equals(fieldName)) {
                updateExtendAttributes(task, fieldValue);
                continue;
            }

            // 处理普通属性
            updateSimpleProperty(task, fieldMap, fieldName, fieldValue);
        }
    }

    /**
     * 更新简单属性
     * @param task 任务对象
     * @param fieldMap 字段映射
     * @param fieldName 字段名
     * @param fieldValue 字段值
     */
    private void updateSimpleProperty(ProductionTask task, Map<String, Field> fieldMap, String fieldName, JsonNode fieldValue) {
        Field field = fieldMap.get(fieldName);
        if (field == null) {
            log.warn("字段 {} 在 ProductionTask 中不存在，跳过更新", fieldName);
            return;
        }

        if (fieldValue.isNull()) {
            log.debug("字段 {} 的值为 null，跳过更新", fieldName);
            return;
        }

        try {
            field.setAccessible(true);
            Object convertedValue = convertJsonValueToFieldType(fieldValue, field.getType());

            // 特殊处理 LocalDateTime 类型
            if (field.getType() == LocalDateTime.class && convertedValue != null) {
                if (convertedValue instanceof String) {
                    convertedValue = convertToLocalDateTime((String) convertedValue);
                } else if (!(convertedValue instanceof LocalDateTime)) {
                    convertedValue = convertToLocalDateTime(convertedValue.toString());
                }
            }

            field.set(task, convertedValue);
            log.debug("成功更新字段 {}，新值：{}", fieldName, convertedValue);
        } catch (Exception e) {
            log.error("更新字段 {} 失败：{}", fieldName, e.getMessage(), e);
        }
    }

    /**
     * 转换 JSON 值到字段类型
     * @param jsonValue JSON 值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    private Object convertJsonValueToFieldType(JsonNode jsonValue, Class<?> targetType) {
        if (jsonValue.isNull()) {
            return null;
        }

        // 字符串类型
        if (targetType == String.class) {
            return jsonValue.asText();
        }

        // 整数类型
        if (targetType == Integer.class || targetType == int.class) {
            return jsonValue.asInt();
        }

        // 长整数类型
        if (targetType == Long.class || targetType == long.class) {
            return jsonValue.asLong();
        }

        // 布尔类型
        if (targetType == Boolean.class || targetType == boolean.class) {
            return jsonValue.asBoolean();
        }

        // BigDecimal 类型
        if (targetType == BigDecimal.class) {
            return new BigDecimal(jsonValue.asText());
        }

        // LocalDateTime 类型 - 返回字符串，在 updateSimpleProperty 中进行转换
        if (targetType == LocalDateTime.class) {
            return jsonValue.asText();
        }

        // 其他类型暂不支持
        log.warn("不支持的字段类型：{}，使用字符串值", targetType.getSimpleName());
        return jsonValue.asText();
    }

    /**
     * 更新扩展属性
     * @param task 任务对象
     * @param extendAttrNode 扩展属性 JSON 节点
     */
    private void updateExtendAttributes(ProductionTask task, JsonNode extendAttrNode) {
        if (extendAttrNode.isNull() || !extendAttrNode.isObject()) {
            log.debug("扩展属性为空或不是对象类型，跳过更新");
            return;
        }

        try {
            // 查询现有的扩展属性
            List<ProductionTaskExtend> existingExtends = productionTaskExtendRepository
                    .findByTaskTaskId(task.getTaskId());

            ProductionTaskExtend taskExtend;
            if (CollectionUtils.isEmpty(existingExtends)) {
                // 创建新的扩展属性记录
                taskExtend = new ProductionTaskExtend();
                taskExtend.setTask(task);
                taskExtend.setExtendAttr(new HashMap<>());
            } else {
                // 使用现有的扩展属性记录
                taskExtend = existingExtends.get(0);
                if (taskExtend.getExtendAttr() == null) {
                    taskExtend.setExtendAttr(new HashMap<>());
                }
            }

            // 更新扩展属性
            Map<String, Object> extendAttr = taskExtend.getExtendAttr();
            Iterator<Map.Entry<String, JsonNode>> fieldsIterator = extendAttrNode.fields();
            while (fieldsIterator.hasNext()) {
                Map.Entry<String, JsonNode> entry = fieldsIterator.next();
                String attrName = entry.getKey();
                JsonNode attrValue = entry.getValue();

                if (attrValue.isNull()) {
                    extendAttr.remove(attrName);
                } else {
                    extendAttr.put(attrName, convertJsonNodeToObject(attrValue));
                }
                log.debug("更新扩展属性 {}，新值：{}", attrName, attrValue);
            }

            // 保存扩展属性
            productionTaskExtendRepository.save(taskExtend);
            log.info("扩展属性更新完成");

        } catch (Exception e) {
            log.error("更新扩展属性失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 将 JsonNode 转换为 Object
     * @param jsonNode JSON 节点
     * @return 转换后的对象
     */
    private Object convertJsonNodeToObject(JsonNode jsonNode) {
        if (jsonNode.isNull()) {
            return null;
        } else if (jsonNode.isBoolean()) {
            return jsonNode.asBoolean();
        } else if (jsonNode.isInt()) {
            return jsonNode.asInt();
        } else if (jsonNode.isLong()) {
            return jsonNode.asLong();
        } else if (jsonNode.isDouble()) {
            return jsonNode.asDouble();
        } else if (jsonNode.isTextual()) {
            return jsonNode.asText();
        } else if (jsonNode.isArray() || jsonNode.isObject()) {
            return JsonUtils.toJson(jsonNode);
        } else {
            return jsonNode.asText();
        }
    }

    /**
     * 转换字符串为 LocalDateTime
     * <p>
     * 支持多种日期时间格式的转换，包括：
     * - yyyy-MM-dd HH:mm:ss
     * - yyyy-MM-dd'T'HH:mm:ss
     * - yyyy-MM-dd HH:mm
     * - yyyy-MM-dd
     * - yyyyMMdd HHmmss
     * - yyyyMMdd
     * </p>
     *
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime 对象，如果输入为空则返回null
     * @throws RuntimeException 当无法解析日期时间字符串时抛出异常
     */
    private LocalDateTime convertToLocalDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }

        try {
            // 尝试使用 DateTimeConverter 工具类
            return DateTimeConverter.convertToLocalDateTime(dateTimeStr);
        } catch (Exception e) {
            log.warn("使用 DateTimeConverter 转换日期失败，尝试其他格式：{}", e.getMessage());

            // 尝试常见的日期格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd'T'HH:mm:ss",
                "yyyy-MM-dd HH:mm",
                "yyyy-MM-dd",
                "yyyyMMdd HHmmss",
                "yyyyMMdd"
            };

            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    if (pattern.contains("HH")) {
                        return LocalDateTime.parse(dateTimeStr, formatter);
                    } else {
                        return LocalDateTime.parse(dateTimeStr + " 00:00:00",
                                DateTimeFormatter.ofPattern(pattern + " HH:mm:ss"));
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }

            throw new RuntimeException("无法解析日期时间字符串：" + dateTimeStr);
        }
    }



}
